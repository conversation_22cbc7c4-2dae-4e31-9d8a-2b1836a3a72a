{"Nav": {"Home": "Home", "StyledComponents": "Styled components", "EnvironmentVariables": "Environment variables", "ReactHookForm": "React hook form", "StaticFiles": "Static files", "Theme": "Theme", "i18n": "i18n", "TranslatorExample": "Translator<PERSON><PERSON><PERSON>"}, "Account": {"LanguageEnglishUK": "English", "LanguageFrench": "French", "LanguagePolish": "Polish"}, "Hello": "Hello", "TestMsg": "Multilingual example", "TranslatorExample": "This is an example of multilingual text with a variable: ({{name}})"}