import React from 'react';

import { NavLinkProps } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import { NavigationBar, StyledNavigationItem } from './LocalNavigation.styles';

const NavigationItem = (props: NavLinkProps): JSX.Element => {
  return <StyledNavigationItem {...props}>{props.children}</StyledNavigationItem>;
};

export const LocalNavigation = (): JSX.Element => {
  const { t } = useTranslation();
  return (
    <NavigationBar>
      <NavigationItem to="/">{t('Nav.Home')}</NavigationItem>
      <NavigationItem to="/theme">{t('Nav.Theme')}</NavigationItem>
      <NavigationItem to="/environment-variables">{t('Nav.EnvironmentVariables')}</NavigationItem>
      <NavigationItem to="/react-hook-form">{t('Nav.ReactHookForm')}</NavigationItem>
      <NavigationItem to="/static-files">{t('Nav.StaticFiles')}</NavigationItem>
      <NavigationItem to="/i18n">{t('Nav.i18n')}</NavigationItem>
    </NavigationBar>
  );
};
