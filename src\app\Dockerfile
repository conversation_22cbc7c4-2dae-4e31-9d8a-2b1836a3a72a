ARG versionNo=0.0.0.0
ARG KENDO_UI_LICENSE

# stage: 1 — install & build
FROM node:22.16-alpine AS react-build
ARG versionNo
ARG KENDO_UI_LICENSE
LABEL autodelete=${versionNo}

WORKDIR /app
COPY ./src/app ./
RUN yarn install
RUN npx kendo-ui-license activate
RUN yarn run lint
RUN yarn run build

# stage: 2 — run tests
FROM react-build AS testrunner
ARG versionNo
ARG KENDO_UI_LICENSE
LABEL autodelete=${versionNo}

WORKDIR /app
ENTRYPOINT yarn run test:ci

# stage: 3 — the production environment
FROM nginxinc/nginx-unprivileged:alpine
# Nginx config
RUN rm -rf /etc/nginx/conf.d
COPY ./src/app/config/nginx /etc/nginx

# Static build
COPY --from=react-build /app/dist /usr/share/nginx/html

# Default port exposure
EXPOSE 8080

# Start Nginx server
CMD ["/bin/sh", "-c", "nginx -g \"daemon off;\""]