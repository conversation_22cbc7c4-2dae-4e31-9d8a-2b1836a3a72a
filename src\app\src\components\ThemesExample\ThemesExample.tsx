import React from 'react';

import { DarkModeSwitch } from 'components/DarkModeSwitch/DarkModeSwitch';
import { PhlexPre } from 'components/PhlexPre/PhlexPre';
import { PhlexBreadcrumb, PhlexLayout } from 'phlex-core-ui';
import { Content } from 'shared/styles/Shared.styles';

import codeSnippets from './ThemesExample.codesnippets';

const breadcrumbs = [
  {
    key: 'home',
    text: 'Home',
    active: 'false' as const,
    path: '/',
  },
  {
    key: 'theme',
    text: 'Theme',
    active: 'true' as const,
    path: '/theme',
  },
];

export const ThemesExample = (): JSX.Element => {
  const { PageTopBar, PageHeading } = PhlexLayout;

  return (
    <>
      <PageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
      </PageTopBar>
      <Content>
        <PageHeading>Theme Switching with Dark and Default Themes</PageHeading>
        <p>
          This feature allows users to switch between two themes: Dark and Default. <br />
          The user can choose their preferred theme, or they can select the &quot;Auto&quot; option, which sets the theme based on the
          operating system&apos;s current theme. <br />
          The chosen theme preference is stored in the local storage, so it persists across page reloads and future visits to the app.
        </p>
        <h4>How to add theme support to my app?</h4>
        <p>
          The application needs to be wrapper by <i>PhlexSharedThemeProvider</i>
        </p>
        <PhlexPre codeSnippet={codeSnippets.howToAdd} />
        <i>PhlexSharedThemeProvider</i> under the hood applies ThemeProvider from the styled-components library and applies standard themes
        used across Phlex applications.
        <br />
        <h4>What are the variables used in the theme? </h4>
        <p>
          Themes are part of <i>phlex-core-ui</i> library. Definition, schema, and values can be found{' '}
          <a target="blank" href="https://phlexglobal.visualstudio.com/Phlex.Core/_git/Phlex.Core.UI?path=/src/theme/theme.tsx">
            here
          </a>
        </p>
        <h4>How to use theme variables?</h4>
        <p>
          All styled-components have access to the provided theme, even when they are multiple levels deep. <br />
          See example below
        </p>
        <PhlexPre codeSnippet={codeSnippets.howToUse} />
        <h4>How can I check all available themes?</h4>
        <p>
          You can use <i>useThemeOptions</i> hook
        </p>
        <PhlexPre codeSnippet={codeSnippets.howToCheckThemes} />
        <p>The single option contains the below information</p>
        <PhlexPre codeSnippet={codeSnippets.themeOptions} />
        <h4>Example of usage</h4>
        <DarkModeSwitch />
        <br />
        <br />
        Check the file <i>\src\app\src\components\DarkModeSwitch\DarkModeSwitch.tsx</i> to see example code that switch on/off dark mode.
      </Content>
    </>
  );
};
