import React, { useState } from 'react';

import { SwitchChangeEvent } from '@progress/kendo-react-inputs';
import { useThemeOptions, localStorageSrv, PhlexSwitch } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';

import { SpanWrapper } from './DarkModeSwitch.styles';

const PHLEX_THEME_KEY = 'phlex_theme';
const DARK_THEME_KEY = 'dark';
const DEFAULT_THEME_KEY = 'default';

export const DarkModeSwitch = (): JSX.Element => {
  const { t } = useTranslation();
  const options = useThemeOptions(t);

  const [isDark, setIsDark] = useState<boolean>(localStorageSrv.getValue(PHLEX_THEME_KEY) === DARK_THEME_KEY);

  const onThemeChange = (event: SwitchChangeEvent): void => {
    setIsDark(event.value);
    const themKey = event.value ? DARK_THEME_KEY : DEFAULT_THEME_KEY;
    const phlexTheme = options.find((x) => x.id === themKey);
    localStorageSrv.setValue(PHLEX_THEME_KEY, themKey);
    phlexTheme?.event();
  };

  return (
    <>
      <SpanWrapper>Dark Mode</SpanWrapper>
      <PhlexSwitch checked={isDark} name="dark-mode" onChange={onThemeChange} />
    </>
  );
};
