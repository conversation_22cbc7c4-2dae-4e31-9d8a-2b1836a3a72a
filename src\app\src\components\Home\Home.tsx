import React from 'react';

import { CustomPre } from 'components/PhlexPre/PhlexPre.styles';
import { Content } from 'shared/styles/Shared.styles';

export const Home = (): JSX.Element => {
  const libs = `  "@hookform/resolvers": "^3.3.4",
  "i18next": "^23.11.3",
  "phlex-core-ui": "1.1.519",
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "react-hook-form": "7.51.3",
  "react-i18next": "^14.1.1",
  "react-router-dom": "6.22.0",
  "react-toastify": "^10.0.5",
  "styled-components": "6.1.8",
  "yup": "^1.4.0"`;

  return (
    <Content>
      <h1>Welcome to the Axon Demo App documentation</h1>
      <p>
        This demo app showcases the main features of a template project, designed to help you kickstart your development process. The app
        demonstrates the following features:
      </p>
      <ul>
        <li>Theme switching with dark and default themes</li>
        <li>Environment variables configuration</li>
        <li>React hook form integration</li>
        <li>Static files and asset management</li>
        <li>Multi-language support with i18next</li>
      </ul>
      <p>The app is built using the following libraries and versions:</p>
      <CustomPre>{libs}</CustomPre>
      <p>
        The documentation will guide you through each feature, providing examples and explanations of how to utilize them in your own
        project. By using this demo app as a starting point, you can accelerate your development process and create a powerful, feature-rich
        application with ease.
      </p>
      <p>Let&apos;s get started by exploring the features in detail!</p>
    </Content>
  );
};
