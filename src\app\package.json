{"name": "ui", "version": "0.0.0", "private": true, "dependencies": {"axios": "latest", "axon-core-ui-shared": "latest", "@hookform/resolvers": "^3.9.1", "i18next": "^24.2.0", "phlex-core-ui": "latest", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "7.54.2", "react-i18next": "^15.4.0", "react-router-dom": "7.4.0", "react-toastify": "^11.0.2", "styled-components": "6.1.16", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@babel/runtime": "^7.26.0", "@eslint/compat": "^1.2.5", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@progress/kendo-drawing": "^1.21.2", "@progress/kendo-licensing": "^1.3.5", "@progress/kendo-react-animation": "^9.1.0", "@progress/kendo-react-buttons": "^9.1.0", "@progress/kendo-react-dropdowns": "^9.1.0", "@progress/kendo-react-inputs": "^9.1.0", "@progress/kendo-react-intl": "^9.1.0", "@progress/kendo-react-treeview": "^9.1.0", "@progress/kendo-theme-default": "^10.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/copy-webpack-plugin": "^10.1.3", "@types/enzyme": "3.10.18", "@types/fork-ts-checker-webpack-plugin": "^0.4.5", "@types/jest": "^29.5.14", "@types/node": "^22.10.3", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@types/react-router-dom": "5.3.3", "@types/react-toastify": "^4.1.0", "@types/styled-components": "5.1.34", "@types/webpack": "^5.28.5", "@types/webpack-dev-server": "^4.7.2", "@types/yup": "^0.32.0", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "babel-jest": "^29.7.0", "babel-loader": "^9.2.1", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "dotenv": "^16.4.7", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-testing-library": "^7.1.1", "eslint-webpack-plugin": "^4.2.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-localstorage-mock": "^2.4.26", "prettier": "^3.4.2", "style-loader": "^4.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.7.2", "webpack": "^5.97.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0", "webpack-shell-plugin-next": "^2.3.2"}, "jest-junit": {"outputDirectory": "/testResults", "outputName": "test-report.xml"}, "resolutions": {"strip-ansi": "6.0.1", "string-width": "4.2.3", "wrap-ansi": "7.0.0"}, "scripts": {"start": "bash ./config/generate-ssl.sh && yarn run webpack serve --config webpack.dev.config.ts", "build": "yarn run webpack --config webpack.prod.config.ts", "test": "jest --passWithNoTests --env=jsdom", "test:coverage": "jest --passWithNoTests --coverage --env=jsdom", "test:ci": "jest --ci --reporters=default --reporters=jest-junit --passWithNoTests --env=jsdom", "lint": "eslint", "lint-fix": "eslint --fix", "vs-code-launch": "yarn install && yarn start"}}