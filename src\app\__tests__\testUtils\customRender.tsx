import React, { ComponentType, ReactElement } from 'react';

import { RenderResult, render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';

const Providers = ({ children }: React.PropsWithChildren): JSX.Element => {
  return <ThemeProvider theme={{}}>{children}</ThemeProvider>;
};

const customRender = (ui: ReactElement, options?: any): RenderResult<any> => {
  return render(ui, { wrapper: Providers as ComponentType, ...options });
};

const renderWithRouter = (ui: ReactElement): any => {
  return {
    ...render(
      <MemoryRouter initialEntries={[{ path: '/test-route' }]}>
        <Providers>{ui}</Providers>
      </MemoryRouter>
    ),
    history,
  };
};
export { customRender, renderWithRouter };
