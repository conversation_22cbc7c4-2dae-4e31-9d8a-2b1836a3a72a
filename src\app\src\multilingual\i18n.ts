import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import translationEN from './en.json';
import translationFR from './fr.json';

const resources = {
  englishUK: {
    translation: translationEN,
  },
  french: {
    translation: translationFR,
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: 'englishUK',
  fallbackLng: 'englishUK',
  interpolation: {},
});

export default i18n;
