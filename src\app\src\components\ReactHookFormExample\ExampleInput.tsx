/* eslint-disable react/display-name */
import React, { ComponentType, FunctionComponent } from 'react';

import { InputChangeEvent } from '@progress/kendo-react-inputs';
import { PhlexInput } from 'phlex-core-ui';
import { IPhlexInputProps } from 'phlex-core-ui/build/src/controls/PhlexInput/PhlexInput';
import { useFormContext, useController } from 'react-hook-form';

const withReactHookFormContext =
  <T extends { name: string }>(BaseComponent: ComponentType<T>): FunctionComponent<T> =>
  (props: T) => {
    const { name } = props;
    const { control, trigger } = useFormContext();

    const {
      field: { ...inputProps },
    } = useController({
      name,
      control,
    });

    const onChangeHandler = (e: InputChangeEvent): void => {
      inputProps.onChange(e);
      trigger(name);
    };

    const onBlurHandler = (e: React.FocusEvent<HTMLInputElement>): void => {
      inputProps.onChange(e);
      trigger(name);
    };
    return <BaseComponent onChange={onChangeHandler} onBlur={onBlurHandler} {...props} name={name} />;
  };

export default withReactHookFormContext<IPhlexInputProps>(PhlexInput);
