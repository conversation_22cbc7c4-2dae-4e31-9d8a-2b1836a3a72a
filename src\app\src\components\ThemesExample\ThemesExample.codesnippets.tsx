export default {
  howToAdd: `
  import { PhlexSharedThemeProvider } from 'phlex-core-ui';
  
  const App = () => {
  
    // ... App code
  
   retrun (
    <PhlexSharedThemeProvider>
    // ... App components
    </PhlexSharedThemeProvider>
   )
  }
  
  `,
  howToUse: `
  const Button = styled.button\`
  background-color: \${(props) => props.theme.colors.backgroundSec};
\`;
  `,
  howToCheckThemes: `
  import { useTranslation } from 'react-i18next';
  import { useThemeOptions } from 'phlex-core-ui';

  const Component = () => {
  
    const { t } = useTranslation();
    const options = useThemeOptions(t);

    return ...
  }
  `,
  themeOptions: `
    {
      id: string;                 // Id of theme e.g dark, default
      text: string;               // Userfriendly name
      event: () => void;          // A method that, when called, sets the selected theme 
      eventName: string;          // Name of the event listener of the above method
    }
  `,
};
