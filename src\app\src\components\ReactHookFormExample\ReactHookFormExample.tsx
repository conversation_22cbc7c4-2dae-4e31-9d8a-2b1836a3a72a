import React from 'react';

import { PhlexBreadcrumb, PhlexLayout } from 'phlex-core-ui';
import { Content } from 'shared/styles/Shared.styles';

import { ExampleForm } from './ExampleForm';

const breadcrumbs = [
  {
    key: 'home',
    text: 'Home',
    active: 'false' as const,
    path: '/',
  },
  {
    key: 'react-hook-form',
    text: 'React Hook Form',
    active: 'true' as const,
    path: '/react-hook-form',
  },
];

export const ReactHookFormExample = (): JSX.Element => {
  const { PageTopBar, PageHeading } = PhlexLayout;
  return (
    <>
      <PageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
      </PageTopBar>
      <Content>
        <PageHeading>React Hook Form Integration</PageHeading>
        <p>
          Forms are implemented using the <code>react-hook-form</code> library. Custom controls, such as <code>PhlexInput</code> and{' '}
          <code>PhlexSwitch</code>, are connected to the form management system provided by <code>react-hook-form</code> using the{' '}
          <code>useFormContext</code> method.
        </p>
        <h4>Example | Higher-Order Component: withReactHookFormContext</h4>
        <p>
          A higher-order component (HOC) called <code>withReactHookFormContext</code> is available to wrap custom controls and connect them
          to the form context. This HOC simplifies the process of integrating custom controls with <code>react-hook-form</code> and seamless
          form management.
        </p>
        <h4>Usage</h4>
        <ol>
          <li>
            Import the <code>withReactHookFormContext</code> HOC from the provided module.
          </li>
          <li>
            Wrap your custom control component (e.g., <code>PhlexInput</code> or <code>PhlexSwitch</code>) with the HOC, as shown in the
            following example:
          </li>
        </ol>
        <pre>{`const WrappedPhlexInput = withReactHookFormContext<IPhlexInputProps>(PhlexInput);`}</pre>
        <p>
          Now you can use the wrapped custom control component in your form, and it will automatically connect to the form context provided{' '}
          <code>react-hook-form</code>.
        </p>
        <h4>See how it works</h4> <i>src\app\src\components\ReactHookFormExample</i> <br />
        <br />
        <ExampleForm />
      </Content>
    </>
  );
};
