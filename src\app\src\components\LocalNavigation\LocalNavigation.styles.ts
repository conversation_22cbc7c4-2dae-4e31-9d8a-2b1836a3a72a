﻿import { NavLink } from 'react-router-dom';
import styled from 'styled-components';

export const NavigationBar = styled.div`
  background-color: ${(props) => props.theme.colors.backgroundPri};
  box-shadow: ${(props) => props.theme.shadow};
  display: flex;
  align-items: center;
  height: 3rem;
  position: sticky;
  top: 0;
  z-index: 10;
`;

export const StyledNavigationItem = styled(NavLink)`
  color: ${(props) => props.theme.colors.textPri};
  display: flex;
  align-items: center;
  height: 3rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-decoration: none;
  transition: background-color 300ms;

  &:hover,
  &.active {
    background-color: ${(props) => props.theme.colors.backgroundTer};
  }

  &.active {
    font-weight: ${(props) => props.theme.bold};
  }
`;
