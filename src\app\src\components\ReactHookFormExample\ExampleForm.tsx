import React from 'react';

import { PhlexButton } from 'phlex-core-ui';
import { useForm, FormProvider } from 'react-hook-form';

import { FormValues } from './ExampleForm.types';
import ExampleInput from './ExampleInput';

export const ExampleForm = (): JSX.Element => {
  const methods = useForm<FormValues>();

  const onSubmit = (data: FormValues): void => alert(`Hello ${data.firstName} ${data.secondName}!`);

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>
        <div>
          <ExampleInput label="First name" name="firstName" />
        </div>
        <div>
          <ExampleInput label="Second name" name="secondName" />
        </div>
        <div>
          <ExampleInput label="Age" name="age" type="number" />
        </div>
        <PhlexButton label="Click" type="submit" />
      </form>
    </FormProvider>
  );
};
