import React from 'react';

import { EnvironmentVariablesExample, Home, I18nExample, ReactHookFormExample, StaticFileExample, ThemesExample } from 'components';
import { PhlexToastContainer } from 'phlex-core-ui';
import { Route, Routes } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import setupAxiosInterceptors from 'shared/services/auth/axios-interceptor';
import './multilingual/i18n';

setupAxiosInterceptors();

const App = (): JSX.Element => {
  return (
    <>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/theme" element={<ThemesExample />} />
        <Route path="/environment-variables" element={<EnvironmentVariablesExample />} />
        <Route path="/react-hook-form" element={<ReactHookFormExample />} />
        <Route path="/static-files" element={<StaticFileExample />} />
        <Route path="/i18n" element={<I18nExample />} />
      </Routes>
      <PhlexToastContainer />
      <ToastContainer hideProgressBar />
    </>
  );
};

export default App;
