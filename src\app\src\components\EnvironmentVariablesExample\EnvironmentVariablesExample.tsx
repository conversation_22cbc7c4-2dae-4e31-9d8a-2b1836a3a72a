import React from 'react';

import { PhlexBreadcrumb, PhlexLayout } from 'phlex-core-ui';
import { environmentVariableSrv } from 'shared/services';
import { Content } from 'shared/styles/Shared.styles';

const breadcrumbs = [
  {
    key: 'home',
    text: 'Home',
    active: 'false' as const,
    path: '/',
  },
  {
    key: 'theme',
    text: 'Environment variables',
    active: 'true' as const,
    path: '/environment-variables',
  },
];

export const EnvironmentVariablesExample = (): JSX.Element => {
  const { PageTopBar, PageHeading } = PhlexLayout;

  return (
    <>
      <PageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
      </PageTopBar>
      <Content>
        <PageHeading>Environment Variables Configuration</PageHeading>
        <p>
          This feature allows you to manage and configure environment variables for your app using a config map file. <br />
          These variables can be used to store configuration settings that are <u>not confidential</u>, as{' '}
          <u>they can be accessible by the end user</u>.
        </p>
        <h4>Key Components</h4>
        <li>
          <b>deploy\helm\ui-chart\templates\configmap.yaml file</b> &#8211; a file where environment variables are defined for production.
        </li>
        <li>
          <b>src\app\assets\env-config.js</b> &#8211; JavaScript file that contains the environment variables for local development. Example
          env-config.js file:
        </li>
        <pre>{`window._env_ = { ...window?._env_, UI_PUBLIC_URL: 'https://localhost:4070/' };`}</pre>
        <li>
          <b>Static window object</b> &#8211; the environment variables are attached to the window object, making them globally accessible
          within your app.
        </li>
        <h4>Example of usage</h4>
        See <b>{`src\\app\\src\\shared\\services\\env-variables-srv.ts`}</b> for the code. <br />
        Example of env variable: <b> {environmentVariableSrv.getVariable('UI_PUBLIC_URL')}</b>
      </Content>
    </>
  );
};
