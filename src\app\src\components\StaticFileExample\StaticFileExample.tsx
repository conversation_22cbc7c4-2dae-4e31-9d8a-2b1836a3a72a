import React from 'react';

import { PhlexBreadcrumb, PhlexLayout } from 'phlex-core-ui';
import { Content } from 'shared/styles/Shared.styles';

const breadcrumbs = [
  {
    key: 'home',
    text: 'Home',
    active: 'false' as const,
    path: '/',
  },
  {
    key: 'static-files',
    text: 'Static files',
    active: 'true' as const,
    path: '/static-files',
  },
];

export const StaticFileExample = (): JSX.Element => {
  const { PageTopBar, PageHeading } = PhlexLayout;

  return (
    <>
      <PageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
      </PageTopBar>
      <Content>
        <PageHeading>Static Files and Asset Management</PageHeading>
        <p>
          This feature allows you to manage and include static files, such as images, in your app by placing them in the assets directory.
          The Webpack plugin &quot;CopyWebpackPlugin&quot; is configured to copy these files to the final output during both development and
          production builds.
        </p>

        <h4>Key Components</h4>
        <ul>
          <li>
            <b>assets directory</b> &#8211; a folder where you can store your static files, such as images, fonts, or any other static
            assets your app requires. <br />
          </li>
          <li>
            <b>CopyWebpackPlugin</b> &#8211; a Webpack plugin configured to copy the contents of the assets directory to the final output
            during the build process.
          </li>
        </ul>
        <h4>Usage</h4>
        <ol>
          <li>
            Place your static files (e.g., images, fonts, etc.) in the <code>assets</code> directory within your project.
          </li>
          <li>
            During the build process, the &quot;CopyWebpackPlugin&quot; will copy the contents of the <code>assets</code> directory to the
            final output folder (e.g., <code>dist</code> or <code>build</code>).
          </li>
          <li>
            In your app, reference the static files using their relative paths from the output folder. <br />
            Example: <code>{` <img src="assets/react-logo.png" alt="Logo" />`}</code>{' '}
          </li>
        </ol>
        <h4>Optimization Tip</h4>
        <p>
          To ensure optimal performance, compress your static assets before placing them in the <code>assets</code> directory. This will
          help reduce the size of the final build and improve the app&apos;s loading time. There are various tools available online to
          compress images and other static files without compromising their quality.
        </p>
        <h4>Example of usage</h4>
        <img src="assets/react-logo.png" alt="Logo" />
      </Content>
    </>
  );
};
