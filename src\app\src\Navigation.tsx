import React from 'react';

import { PhlexNavItem } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';
import { removeTrailingSlashFromPath } from 'shared/services/urlUtils';
import './multilingual/i18n';

interface NavigationProps {
  basepath: string;
}

const Navigation = ({ basepath }: NavigationProps): JSX.Element => {
  const { t } = useTranslation();
  const cleanBasePath = removeTrailingSlashFromPath(basepath);

  return (
    <>
      <PhlexNavItem text={t('Nav.Theme')} to={`${cleanBasePath}/theme`} />
      <PhlexNavItem text={t('Nav.Multilingual')} to={`${cleanBasePath}/i18n`} />
      <PhlexNavItem text={t('Nav.StaticFiles')} to={`${cleanBasePath}/static-files`} />
      <PhlexNavItem text={t('Nav.ReactHookForm')} to={`${cleanBasePath}/react-hook-form`} />
      <PhlexNavItem text={t('Nav.EnvironmentVariables')} to={`${cleanBasePath}/environment-variables`} />
      <PhlexNavItem text={t('Nav.Communications')} to={`${cleanBasePath}/i18n`} />
    </>
  );
};

export default Navigation;
