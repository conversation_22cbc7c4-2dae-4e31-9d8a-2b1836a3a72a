import React from 'react';

import { EnvironmentVariablesExample, ThemesExample, ReactHookFormExample, StaticFileExample, I18nExample, Home } from 'components';
import { LocalNavigation } from 'components/LocalNavigation/LocalNavigation';
import { PhlexSharedThemeProvider, PhlexToastContainer } from 'phlex-core-ui';
import * as ReactDOMClient from 'react-dom/client';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';

import { GlobalStyles } from './App.styles';
import setupAxiosInterceptors from 'shared/services/auth/axios-interceptor';

import './multilingual/i18n.ts';
import '@progress/kendo-theme-default/dist/all.css';

setupAxiosInterceptors();

const container = document.getElementById('root');
const root = ReactDOMClient.createRoot(container);

root.render(
  <React.StrictMode>
    <BrowserRouter>
      <PhlexSharedThemeProvider>
        <PhlexToastContainer />
        <ToastContainer hideProgressBar />
        <GlobalStyles />
        <LocalNavigation />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/theme" element={<ThemesExample />} />
          <Route path="/environment-variables" element={<EnvironmentVariablesExample />} />
          <Route path="/react-hook-form" element={<ReactHookFormExample />} />
          <Route path="/static-files" element={<StaticFileExample />} />
          <Route path="/i18n" element={<I18nExample />} />
        </Routes>
      </PhlexSharedThemeProvider>
    </BrowserRouter>
  </React.StrictMode>
);
