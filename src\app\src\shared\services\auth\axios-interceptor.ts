import { ERROR_CODES } from 'constants/errorCodes';

import Axios, { AxiosError, InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import { environmentVariableSrv as env } from 'shared/services';

const redirectToSignIn = (errorCode: string): void => {
  const redirectUrl = `/sign-in?error=${errorCode}`;
  window.location.href = redirectUrl;
};

const onRequest = async (config: InternalAxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
  if (env.getVariable('LOCAL_TOKEN')) {
    config.headers.Authorization = `Bearer ${env.getVariable('LOCAL_TOKEN')}`;
  } else if (env.getVariable('AXON_SHARED_LOCAL_COOKIE')) {
    config.withCredentials = true;
  }

  return config;
};

const onRequestError = (error: AxiosError): Promise<AxiosError> => {
  return Promise.reject(error);
};

const onResponse = (response: AxiosResponse): AxiosResponse => {
  return response;
};

const onResponseError = (error: AxiosError): Promise<AxiosError> => {
  if (error.config && error.status === 401) {
    redirectToSignIn(ERROR_CODES.AUTHENTICATION_ERROR);
  }
  return Promise.reject(error);
};

const setupAxiosInterceptors = (): void => {
  Axios.interceptors.request.use(onRequest, onRequestError);
  Axios.interceptors.response.use(onResponse, onResponseError);
};

export default setupAxiosInterceptors;
