import React from 'react';

import { DropDownListChangeEvent } from '@progress/kendo-react-dropdowns';
import i18n from 'i18next';
import { PhlexBreadcrumb, PhlexDropdown, PhlexLayout, useLanguageOptions } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';
import { Content } from 'shared/styles/Shared.styles';

const breadcrumbs = [
  {
    key: 'home',
    text: 'Home',
    active: 'false' as const,
    path: '/',
  },
  {
    key: 'i18n',
    text: 'i18n',
    active: 'true' as const,
    path: '/i18n',
  },
];

export const I18nExample = (): JSX.Element => {
  const { t } = useTranslation();
  const languageOptions = useLanguageOptions(t);
  const { PageTopBar, PageHeading } = PhlexLayout;

  const onLangChange = (event: DropDownListChangeEvent): void => {
    const lang = languageOptions.find((x) => x.text === event.value);
    i18n.changeLanguage(lang?.id);
  };

  return (
    <>
      <PageTopBar>
        <PhlexBreadcrumb options={breadcrumbs} />
      </PageTopBar>
      <Content>
        <PageHeading>Multi-language Support with i18next</PageHeading>
        <p>
          The i18next library is used to handle multi-language texts. All supported languages are provided by the{' '}
          <code>useLanguageOptions</code> hook from <code>phlex-core-ui</code>. <br />
          The multi-language configuration is located in the <code>src/multilingual</code> directory.
        </p>
        <h4>Translation JSON Files</h4>
        <p>
          Texts for translation are stored in language-specific JSON files. These files support dynamic values (variables), allowing for
          text customization based on context.
        </p>
        <h4>Usage</h4>
        <ol>
          <li>
            Import the <code>useLanguageOptions</code> hook to access the available languages.
          </li>
          <li>
            Add translation JSON files for each supported language in the <code>src/multilingual</code> directory.
          </li>
          <li>
            Use i18next to display translated texts in the user interface based on the selected language. <br />
            <code>{`const { t } = useTranslation();`}</code> <br /> <code>{`t('Nav.Home')`}</code>{' '}
          </li>
        </ol>
        <p>
          With this feature, the application can easily manage and display multi-language texts, providing an enhanced user experience for
          users of different languages.
        </p>
        <h4>Example</h4>
        <p>Multilingual texts:</p>
        <ul>
          <li>{t('Nav.Home')}</li>
          <li>{t('Nav.StyledComponents')}</li>
          <li>{t('Nav.EnvironmentVariables')}</li>
          <li>{t('Nav.ReactHookForm')}</li>
          <li>{t('Nav.StaticFiles')}</li>
          <li>{t('TranslatorExample', { name: 'Tomasz Krupa' })}</li>
        </ul>
        <PhlexDropdown
          label="Language"
          data={languageOptions.map((x) => x.text)}
          width="standard"
          name="languageDropdown"
          onChange={onLangChange}
        />
      </Content>
    </>
  );
};
