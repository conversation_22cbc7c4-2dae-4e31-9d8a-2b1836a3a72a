import React from 'react';

import { TestExample } from '../../../src/components/TestExample/TestExample';
import { render, fireEvent } from '../../testUtils';

describe('TestExample', () => {
  it('renders the button with provided label', () => {
    const { getByText } = render(<TestExample onClick={() => jest.fn} label="Test Button" />);
    expect(getByText('Test Button')).toBeInTheDocument();
  });
  it('calls onClick prop when clicked', () => {
    const handleClick = jest.fn();
    const { getByText } = render(<TestExample onClick={handleClick} label="Test Button" />);
    fireEvent.click(getByText('Test Button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  it('does not call onClick prop when clicked and disabled', () => {
    const handleClick = jest.fn();
    const { getByText } = render(<TestExample onClick={handleClick} label="Test Button" disabled />);
    fireEvent.click(getByText('Test Button'));
    expect(handleClick).toHaveBeenCalledTimes(0);
  });
});
